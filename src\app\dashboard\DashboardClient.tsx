"use client";

import {
  CurrencySelector,
  SupportedCurrency,
} from "@/components/dashboard/currency-selector";
import { PortfolioCompositionChart } from "@/components/dashboard/portfolio-composition-chart";
import { PortfolioMultiSelect } from "@/components/dashboard/portfolio-multi-select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Info } from "lucide-react";
import { useState } from "react";

export default function DashboardClient() {
  const [selectedPortfolios, setSelectedPortfolios] = useState<string[]>([]);
  const [displayCurrency, setDisplayCurrency] =
    useState<SupportedCurrency>("EUR");

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      {/* <PERSON> Header */}
      <div className="flex flex-wrap gap-12 items-center mb-8 justify-between">
        <div>
          <h1 className="text-5xl font-bold text-gray-900 dark:text-gray-50 mb-2">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 flex items-center gap-2">
            Analiză completă a portofoliilor tale de investiții
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 inline-block ml-1" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-[220px]">
                  Toate valorile afișate sunt estimative și sunt rotunjite
                  pentru claritate. Pot varia ușor față de valorile reale din
                  cauza cursurilor de schimb și a rotunjirilor.
                </p>
              </TooltipContent>
            </Tooltip>
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          <div>
            <h3 className="text-md font-medium mb-2 text-gray-900 dark:text-gray-50">
              Filtrează după portofolii:
            </h3>
            <PortfolioMultiSelect
              selectedPortfolios={selectedPortfolios}
              onSelectionChange={setSelectedPortfolios}
            />
          </div>

          <div>
            <h3 className="text-md font-medium mb-2 text-gray-900 dark:text-gray-50">
              Moneda de afișare:
            </h3>
            <CurrencySelector
              selectedCurrency={displayCurrency}
              onCurrencyChange={setDisplayCurrency}
            />
          </div>
        </div>
      </div>

      {/* Dashboard Grid Layout */}
      <div className="grid gap-6">
        {/* Row 1: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 1 - Performance Overview (2/3 width) */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>
                  Performanța{" "}
                  {selectedPortfolios.length === 1
                    ? "Portofoliului"
                    : "Portofoliilor"}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-lg font-medium mb-2">
                    Grafic de performanță
                  </div>
                  <div className="text-sm">
                    Evoluția valorii portofoliului în timp
                  </div>
                  <div className="text-xs mt-2 text-orange-500">
                    În dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Card 2 - Portfolio Composition (1/3 width) */}
          <div className="lg:col-span-1">
            <PortfolioCompositionChart
              selectedPortfolios={selectedPortfolios}
              displayCurrency={displayCurrency}
            />
          </div>
        </div>

        {/* Row 2: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 3 - Dividends (2/3 width) */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Dividende</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-lg font-medium mb-2">
                    Istoric dividende
                  </div>
                  <div className="text-sm">
                    Grafic cu dividendele primite pe luni
                  </div>
                  <div className="text-xs mt-2 text-orange-500">
                    În dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Card 4 - Key Metrics (1/3 width) */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Performanță</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Valoare totală
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Câștig/Pierdere
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Randament
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Nr. poziții
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Diversificare
                  </span>
                </div>
                <div className="mt-6 pt-4 border-t">
                  <div className="text-center text-xs text-orange-500">
                    Metrici avansate în dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Row 3: 1 full-width card */}
        <div className="grid grid-cols-1 gap-6 h-[300px]">
          {/* Card 5 - Holdings Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Companiile Mele</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-full flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-lg font-medium mb-2">
                    Tabel cu pozițiile
                  </div>
                  <div className="text-sm">
                    Lista tuturor companiilor investite
                  </div>
                  <div className="text-xs mt-2 text-orange-500">
                    În dezvoltare
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
