import { hasuraQuery } from "./hasura";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import {
  getRequiredExchangeRates,
  fetchExchangeRatesFromDB,
  checkExchangeRateAssetsExist,
  fetchMissingExchangeRates,
  convertAmount,
  extractPortfolioCurrencies,
  ExchangeRateMap,
} from "./exchange-rates";

// Types for dashboard data
export interface PortfolioCompositionItem {
  name: string;
  value: number;
  percentage: number;
  ticker?: string; // Only for positions view
}

export interface PortfolioComposition {
  sector: PortfolioCompositionItem[];
  industry: PortfolioCompositionItem[];
  currency: PortfolioCompositionItem[];
  country: PortfolioCompositionItem[];
  assetType: PortfolioCompositionItem[];
  positions: PortfolioCompositionItem[];
  totalValue: number;
  displayCurrency: SupportedCurrency;
}

export interface TransactionWithAssetDetails {
  id: string;
  portfolio_id: string;
  ticker: string;
  price: number;
  quantity: number;
  transaction_date: string;
  transaction_type: "BUY" | "SELL";
  asset?: {
    asset_id: number;
    ticker: string;
    name: string;
    company: string;
    sector?: { name: string };
    industry?: { name: string };
    currency?: { code: string; name: string; symbol: string };
    country?: { code: string; name: string };
    asset_type?: { name: string };
  };
  latest_price?: {
    close_price: number;
    date: string;
  };
}

// GraphQL Queries

/**
 * Get portfolio composition data with asset details and reference tables
 * This query fetches transactions with complete asset information
 */
export const GET_PORTFOLIO_COMPOSITION_DATA = `
  query GetPortfolioCompositionData($portfolioIds: [uuid!]!) {
    ptvuser_transactions(
      where: {portfolio_id: {_in: $portfolioIds}}
      order_by: {transaction_date: desc}
    ) {
      id
      portfolio_id
      ticker
      price
      quantity
      transaction_date
      transaction_type
    }
  }
`;

/**
 * Get asset details with all reference table joins
 */
export const GET_ASSETS_WITH_REFERENCES = `
  query GetAssetsWithReferences($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      asset_id
      ticker
      name
      company
      sector {
        name
      }
      industry {
        name
      }
      currency {
        code
        name
        symbol
      }
      country {
        code
        name
      }
      asset_type {
        name
      }
    }
  }
`;

/**
 * Get latest asset prices for portfolio tickers
 */
export const GET_LATEST_ASSET_PRICES = `
  query GetLatestAssetPrices($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      ticker
      asset_id
      latest_price: asset_prices(
        order_by: {date: desc}
        limit: 1
      ) {
        close_price
        date
      }
    }
  }
`;

// Utility Functions

/**
 * Calculate portfolio holdings from transactions
 */
export function calculatePortfolioHoldings(
  transactions: TransactionWithAssetDetails[]
): Map<
  string,
  { quantity: number; transactions: TransactionWithAssetDetails[] }
> {
  const holdings = new Map<
    string,
    { quantity: number; transactions: TransactionWithAssetDetails[] }
  >();

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, transactions: [] };

    const quantity =
      transaction.transaction_type === "BUY"
        ? transaction.quantity
        : -transaction.quantity;

    holdings.set(ticker, {
      quantity: existing.quantity + quantity,
      transactions: [...existing.transactions, transaction],
    });
  });

  // Filter out holdings with zero or negative quantity
  const filteredHoldings = new Map();
  holdings.forEach((value, key) => {
    if (value.quantity > 0) {
      filteredHoldings.set(key, value);
    }
  });

  return filteredHoldings;
}

/**
 * Calculate portfolio composition by different categories
 */
export function calculatePortfolioComposition(
  transactions: TransactionWithAssetDetails[],
  latestPrices: Map<string, number>,
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): PortfolioComposition {
  const holdings = calculatePortfolioHoldings(transactions);

  // Calculate current values for each holding
  const holdingValues = new Map<string, number>();
  let totalValue = 0;

  holdings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR"; // Default to EUR if no currency info

    // Calculate value in original currency
    const originalValue = holding.quantity * latestPrice;

    // Convert to display currency
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    holdingValues.set(ticker, convertedValue);
    totalValue += convertedValue;
  });

  // Group by different categories
  const sectorMap = new Map<string, number>();
  const industryMap = new Map<string, number>();
  const currencyMap = new Map<string, number>();
  const countryMap = new Map<string, number>();
  const assetTypeMap = new Map<string, number>();
  const positionsArray: PortfolioCompositionItem[] = [];

  holdings.forEach((_, ticker) => {
    const value = holdingValues.get(ticker) || 0;
    const asset = assetData.get(ticker);

    if (value === 0) return;

    // Positions
    positionsArray.push({
      name: `${asset?.name} (${ticker})`,
      ticker,
      value,
      percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
    });

    // Sector
    const sectorName = asset?.sector?.name || "Altele / Diversificat";
    sectorMap.set(sectorName, (sectorMap.get(sectorName) || 0) + value);

    // Industry
    const industryName = asset?.industry?.name || "Altele / Diversificat";
    industryMap.set(industryName, (industryMap.get(industryName) || 0) + value);

    // Currency
    const currencyName =
      asset?.currency?.name || asset?.currency?.code || "Altele";
    currencyMap.set(currencyName, (currencyMap.get(currencyName) || 0) + value);

    // Country
    const countryName = asset?.country?.name || "Diversificat";
    if (countryName === "USA" || countryName === "United States") {
      countryMap.set(
        "United States",
        (countryMap.get("United States") || 0) + value
      );
    } else {
      countryMap.set(countryName, (countryMap.get(countryName) || 0) + value);
    }

    // Asset Type
    let assetTypeName = asset?.asset_type?.name || "Altele";
    // Convert EQUITY to STOCK as requested
    if (assetTypeName === "EQUITY" || assetTypeName === "Common Stock") {
      assetTypeName = "STOCK";
    }
    assetTypeMap.set(
      assetTypeName,
      (assetTypeMap.get(assetTypeName) || 0) + value
    );
  });

  // Convert maps to arrays with percentages
  const createCompositionArray = (
    map: Map<string, number>
  ): PortfolioCompositionItem[] => {
    return Array.from(map.entries())
      .map(([name, value]) => ({
        name,
        value,
        percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
      }))
      .sort((a, b) => b.value - a.value);
  };

  return {
    sector: createCompositionArray(sectorMap),
    industry: createCompositionArray(industryMap),
    currency: createCompositionArray(currencyMap),
    country: createCompositionArray(countryMap),
    assetType: createCompositionArray(assetTypeMap),
    positions: positionsArray.sort((a, b) => b.value - a.value),
    totalValue,
    displayCurrency,
  };
}

/**
 * Fetch portfolio composition data for selected portfolios
 */
export async function getPortfolioCompositionData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioComposition> {
  try {
    if (portfolioIds.length === 0) {
      return {
        sector: [],
        industry: [],
        currency: [],
        country: [],
        assetType: [],
        positions: [],
        totalValue: 0,
        displayCurrency,
      };
    }

    // Fetch transactions with asset details
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];

    if (transactions.length === 0) {
      return {
        sector: [],
        industry: [],
        currency: [],
        country: [],
        assetType: [],
        positions: [],
        totalValue: 0,
        displayCurrency,
      };
    }

    // Get unique tickers for price lookup
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Fetch asset data with references and prices in parallel
    const [assetsResult, pricesResult] = await Promise.all([
      hasuraQuery<{
        ptvuser_asset: Array<{
          asset_id: number;
          ticker: string;
          name: string;
          company: string;
          sector?: { name: string };
          industry?: { name: string };
          currency?: { code: string; name: string; symbol: string };
          country?: { code: string; name: string };
          asset_type?: { name: string };
        }>;
      }>(GET_ASSETS_WITH_REFERENCES, { variables: { tickers: uniqueTickers } }),

      hasuraQuery<{
        ptvuser_asset: Array<{
          ticker: string;
          latest_price: Array<{ close_price: number; date: string }>;
        }>;
      }>(GET_LATEST_ASSET_PRICES, { variables: { tickers: uniqueTickers } }),
    ]);

    const assetData = new Map<string, TransactionWithAssetDetails["asset"]>();
    assetsResult.ptvuser_asset?.forEach((asset) => {
      assetData.set(asset.ticker, asset);
    });

    const latestPrices = new Map<string, number>();
    pricesResult.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        latestPrices.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    // Get portfolio currencies and fetch exchange rates if needed
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);
    const requiredExchangeRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    let exchangeRates = new Map<string, number>();

    if (requiredExchangeRates.length > 0) {
      try {
        // Check which exchange rate assets exist
        const existingAssets = await checkExchangeRateAssetsExist(
          requiredExchangeRates
        );
        const missingAssets = requiredExchangeRates.filter(
          (pair) => !existingAssets.has(pair)
        );

        // Fetch missing exchange rates from EODHD API
        if (missingAssets.length > 0) {
          console.log(
            `Fetching missing exchange rates: ${missingAssets.join(", ")}`
          );
          await fetchMissingExchangeRates(missingAssets);
        }

        // Fetch all exchange rates from database
        exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);
        console.log(
          `Loaded ${exchangeRates.size} exchange rates for currency conversion`
        );
        console.log("exchangeRates", exchangeRates);
      } catch (error) {
        console.error("Error fetching exchange rates:", error);
        // Continue without currency conversion if exchange rates fail
      }
    }

    return calculatePortfolioComposition(
      transactions,
      latestPrices,
      assetData,
      displayCurrency,
      exchangeRates
    );
  } catch (error) {
    console.error("Error fetching portfolio composition data:", error);
    throw new Error(
      "Nu s-au putut încărca datele de compoziție ale portofoliului"
    );
  }
}
